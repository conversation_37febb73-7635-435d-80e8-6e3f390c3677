#!/bin/bash

# Quant-NEX Medical Application Deployment Script
# HIPAA-compliant production deployment with security checks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="quant-nex"
DOCKER_IMAGE="$APP_NAME:latest"
BACKUP_DIR="/backups"
LOG_FILE="/var/log/deploy-$(date +%Y%m%d-%H%M%S).log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Starting pre-deployment checks..."
    
    # Check if running as root (security requirement)
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
    
    # Check required environment variables
    required_vars=(
        "DATABASE_URL"
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "SUPABASE_SERVICE_ROLE_KEY"
        "NEXTAUTH_SECRET"
        "SENTRY_DSN"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            error "Required environment variable $var is not set"
        fi
    done
    
    # Check Docker is running
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running or not accessible"
    fi
    
    # Check disk space (minimum 5GB free)
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 5242880 ]]; then  # 5GB in KB
        error "Insufficient disk space. At least 5GB required."
    fi
    
    success "Pre-deployment checks passed"
}

# Database backup
backup_database() {
    log "Creating database backup..."
    
    if [[ -n "$DATABASE_URL" ]]; then
        # Create backup directory
        mkdir -p "$BACKUP_DIR"
        
        # Generate backup filename
        backup_file="$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S).sql"
        
        # Create database backup (adjust for your database type)
        if [[ "$DATABASE_URL" == *"postgresql"* ]]; then
            pg_dump "$DATABASE_URL" > "$backup_file" || warning "Database backup failed"
        else
            warning "Database backup not implemented for this database type"
        fi
        
        # Compress backup
        if [[ -f "$backup_file" ]]; then
            gzip "$backup_file"
            success "Database backup created: $backup_file.gz"
        fi
    else
        warning "DATABASE_URL not set, skipping database backup"
    fi
}

# Build and deploy application
build_and_deploy() {
    log "Building application..."
    
    # Build Docker image
    docker build -t "$DOCKER_IMAGE" . || error "Docker build failed"
    
    # Tag with timestamp for rollback capability
    timestamp=$(date +%Y%m%d-%H%M%S)
    docker tag "$DOCKER_IMAGE" "$APP_NAME:$timestamp"
    
    log "Deploying application..."
    
    # Stop existing containers gracefully
    if docker ps -q --filter "name=$APP_NAME" | grep -q .; then
        log "Stopping existing containers..."
        docker-compose down --timeout 30
    fi
    
    # Start new containers
    docker-compose up -d --remove-orphans || error "Failed to start containers"
    
    success "Application deployed successfully"
}

# Health checks
health_checks() {
    log "Performing health checks..."
    
    # Wait for application to start
    sleep 30
    
    # Check if containers are running
    if ! docker-compose ps | grep -q "Up"; then
        error "Some containers are not running"
    fi
    
    # Check application health endpoint
    max_attempts=10
    attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s http://localhost:3000/api/health >/dev/null; then
            success "Health check passed"
            break
        else
            log "Health check attempt $attempt/$max_attempts failed, retrying..."
            sleep 10
            ((attempt++))
        fi
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        error "Health checks failed after $max_attempts attempts"
    fi
}

# Security checks
security_checks() {
    log "Performing security checks..."
    
    # Check SSL certificate (if applicable)
    if [[ -f "/etc/nginx/ssl/cert.pem" ]]; then
        cert_expiry=$(openssl x509 -enddate -noout -in /etc/nginx/ssl/cert.pem | cut -d= -f2)
        cert_expiry_epoch=$(date -d "$cert_expiry" +%s)
        current_epoch=$(date +%s)
        days_until_expiry=$(( (cert_expiry_epoch - current_epoch) / 86400 ))
        
        if [[ $days_until_expiry -lt 30 ]]; then
            warning "SSL certificate expires in $days_until_expiry days"
        else
            log "SSL certificate valid for $days_until_expiry days"
        fi
    fi
    
    # Check for security headers
    if curl -s -I http://localhost:3000 | grep -q "X-Frame-Options"; then
        success "Security headers are present"
    else
        warning "Security headers may be missing"
    fi
    
    # Check file permissions
    if [[ -f ".env" ]]; then
        env_perms=$(stat -c "%a" .env)
        if [[ "$env_perms" != "600" ]]; then
            warning "Environment file permissions should be 600, currently $env_perms"
        fi
    fi
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old Docker images..."
    
    # Remove old images (keep last 3 versions)
    docker images "$APP_NAME" --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi "$APP_NAME:" || true
    
    # Remove unused Docker resources
    docker system prune -f --volumes || true
    
    success "Cleanup completed"
}

# Rollback function
rollback() {
    error_msg="$1"
    log "Rolling back deployment due to: $error_msg"
    
    # Get previous image tag
    previous_tag=$(docker images "$APP_NAME" --format "table {{.Tag}}" | sed -n '2p')
    
    if [[ -n "$previous_tag" && "$previous_tag" != "latest" ]]; then
        log "Rolling back to $APP_NAME:$previous_tag"
        
        # Tag previous version as latest
        docker tag "$APP_NAME:$previous_tag" "$DOCKER_IMAGE"
        
        # Restart with previous version
        docker-compose down --timeout 30
        docker-compose up -d --remove-orphans
        
        warning "Rollback completed to version $previous_tag"
    else
        error "No previous version available for rollback"
    fi
}

# Main deployment process
main() {
    log "Starting deployment of $APP_NAME"
    
    # Trap errors for rollback
    trap 'rollback "Deployment failed"' ERR
    
    pre_deployment_checks
    backup_database
    build_and_deploy
    health_checks
    security_checks
    cleanup
    
    success "Deployment completed successfully!"
    log "Application is running at: http://localhost:3000"
    log "Health check: http://localhost:3000/api/health"
    log "Deployment log: $LOG_FILE"
}

# Script options
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback "Manual rollback requested"
        ;;
    "health")
        health_checks
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health|cleanup}"
        echo "  deploy  - Full deployment process (default)"
        echo "  rollback - Rollback to previous version"
        echo "  health  - Run health checks only"
        echo "  cleanup - Clean up old Docker images"
        exit 1
        ;;
esac
