# 📦 Install Dependencies for Vercel Deployment

## Required Dependencies

Please run these commands in your terminal to install the required dependencies:

### Core Dependencies
```bash
npm install @supabase/supabase-js@latest
npm install @supabase/auth-helpers-nextjs@latest
npm install next-auth@latest
npm install prisma@latest
npm install @prisma/client@latest
```

### Additional Dependencies
```bash
npm install @sentry/nextjs@latest
npm install bcryptjs@latest
npm install zod@latest
npm install @upstash/ratelimit@latest
npm install @upstash/redis@latest
```

### Development Dependencies
```bash
npm install --save-dev @types/bcryptjs
npm install --save-dev @types/node
```

## Alternative: Install All at Once

If you prefer to install all dependencies at once:

```bash
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs next-auth prisma @prisma/client @sentry/nextjs bcryptjs zod @upstash/ratelimit @upstash/redis @types/bcryptjs @types/node
```

## Verify Installation

After installation, verify by running:

```bash
npm list --depth=0
```

You should see all the packages listed above.

## Next Steps

After installing dependencies:

1. **Generate Prisma Client**:
   ```bash
   npx prisma generate
   ```

2. **Test the setup**:
   ```bash
   node test-local-deployment.js
   ```

3. **Build the application**:
   ```bash
   npm run build
   ```

If all steps complete successfully, you're ready for Vercel deployment!
