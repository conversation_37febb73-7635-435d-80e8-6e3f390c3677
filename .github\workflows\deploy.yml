name: Deploy Quant-NEX Medical Application

on:
  push:
    branches: [main, production]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Security and compliance checks
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Code quality and testing
  test:
    name: Test and Quality Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type checking
        run: npm run type-check

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm run test
        env:
          NODE_ENV: test

      - name: Generate Prisma client
        run: npx prisma generate

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_TELEMETRY_DISABLED: 1

  # Build and push Docker image
  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [security-scan, test]
    if: github.event_name == 'push'
    outputs:
      image: ${{ steps.image.outputs.image }}
      digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Output image
        id: image
        run: |
          echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}" >> $GITHUB_OUTPUT

  # Deploy to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging.quantnex.com
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying ${{ needs.build.outputs.image }} to staging"
          # Add your staging deployment commands here
          # This could be kubectl, docker-compose, or other deployment tools

  # Deploy to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/production'
    environment:
      name: production
      url: https://quantnex.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "Deploying ${{ needs.build.outputs.image }} to production"
          # Add your production deployment commands here
          # This should include health checks and rollback capabilities

      - name: Run post-deployment tests
        run: |
          # Add post-deployment health checks
          echo "Running post-deployment health checks"

      - name: Notify deployment success
        if: success()
        run: |
          echo "Production deployment successful"
          # Add notification logic (Slack, email, etc.)

      - name: Rollback on failure
        if: failure()
        run: |
          echo "Deployment failed, initiating rollback"
          # Add rollback logic

  # HIPAA compliance check
  compliance-check:
    name: HIPAA Compliance Check
    runs-on: ubuntu-latest
    needs: [test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check for PHI in code
        run: |
          # Scan for potential PHI patterns in code
          echo "Scanning for potential PHI in codebase..."
          
          # Check for hardcoded sensitive data patterns
          if grep -r -E "\b\d{3}-\d{2}-\d{4}\b" --exclude-dir=node_modules --exclude-dir=.git .; then
            echo "❌ Potential SSN found in code"
            exit 1
          fi
          
          if grep -r -E "\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b" --exclude-dir=node_modules --exclude-dir=.git .; then
            echo "❌ Potential credit card number found in code"
            exit 1
          fi
          
          echo "✅ No obvious PHI patterns found in code"

      - name: Check environment variables
        run: |
          # Ensure sensitive environment variables are not hardcoded
          echo "Checking for hardcoded secrets..."
          
          if grep -r -i "password.*=" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" --exclude-dir=node_modules .; then
            echo "❌ Potential hardcoded password found"
            exit 1
          fi
          
          echo "✅ No hardcoded secrets found"

      - name: Audit dependencies
        run: |
          npm audit --audit-level=high
          echo "✅ Dependency audit completed"

  # Performance testing
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './lighthouse.config.js'
          uploadArtifacts: true
          temporaryPublicStorage: true
