// Test Supabase with Service Role Key (bypasses RLS)
// Run this with: node test-auth.js

const supabaseUrl = 'https://tyrvzdkuruzpojowctjo.supabase.co'
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.gUM4hwtMfdvT-Llbv-jAmbza7eQ9URJGMd-FQDDFZjo'

async function testWithServiceRole() {
  console.log('🔐 Testing Supabase with Service Role Key (Admin Access)...\n')
  
  const headers = {
    'apikey': serviceRoleKey,
    'Authorization': `Bearer ${serviceRoleKey}`,
    'Content-Type': 'application/json'
  }
  
  // Test tables with admin access
  const tables = ['profiles', 'medical_professionals', 'patients', 'appointments']
  
  console.log('📋 Testing Tables with Admin Access:')
  
  for (const table of tables) {
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/${table}?select=*&limit=3`, {
        method: 'GET',
        headers: headers
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${table}: ${data.length} records found`)
        
        // Show sample data
        if (data.length > 0) {
          const sample = data[0]
          if (table === 'medical_professionals') {
            console.log(`   → Sample: ${sample.role} in ${sample.department}`)
          } else if (table === 'patients') {
            console.log(`   → Sample: ${sample.first_name} ${sample.last_name} (${sample.medical_record_number})`)
          } else if (table === 'profiles') {
            console.log(`   → Sample: ${sample.full_name || 'No name'} (${sample.id})`)
          }
        }
      } else {
        const errorText = await response.text()
        console.log(`❌ ${table}: ${response.status} - ${errorText}`)
      }
    } catch (error) {
      console.log(`❌ ${table}: Network error - ${error.message}`)
    }
  }
  
  // Test creating a sample record
  console.log('\n🧪 Testing Record Creation:')
  
  try {
    // Try to create a test profile
    const testProfile = {
      id: '00000000-0000-0000-0000-000000000001', // Test UUID
      full_name: 'Test User',
      username: 'testuser'
    }
    
    const createResponse = await fetch(`${supabaseUrl}/rest/v1/profiles`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(testProfile)
    })
    
    if (createResponse.ok) {
      console.log('✅ Record creation: Working')
      
      // Clean up - delete the test record
      await fetch(`${supabaseUrl}/rest/v1/profiles?id=eq.${testProfile.id}`, {
        method: 'DELETE',
        headers: headers
      })
      console.log('✅ Record deletion: Working')
      
    } else {
      const errorText = await createResponse.text()
      console.log(`⚠️  Record creation: ${createResponse.status} - ${errorText}`)
    }
    
  } catch (error) {
    console.log(`❌ Record creation test failed: ${error.message}`)
  }
  
  console.log('\n🎉 Admin Access Test Complete!')
  console.log('\n📊 Summary:')
  console.log('- If you see ✅ with record counts: Tables are working perfectly!')
  console.log('- If you see ❌ errors: There might be schema issues to fix')
  console.log('- Service role key bypasses RLS, so this tests actual table functionality')
}

// Run the test
testWithServiceRole().catch(console.error)
