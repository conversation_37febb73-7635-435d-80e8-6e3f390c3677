// Local deployment test for Quant-NEX Medical Application
// Tests the application with Supabase integration before Vercel deployment
// Run this with: node test-local-deployment.js

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

async function testLocalDeployment() {
  console.log('🏥 Quant-NEX Medical Application - Local Deployment Test\n')
  
  let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
  }
  
  // Test 1: Check environment configuration
  console.log('📋 Test 1: Environment Configuration')
  
  try {
    if (fs.existsSync('.env.local')) {
      const envContent = fs.readFileSync('.env.local', 'utf8')
      const requiredVars = [
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_SUPABASE_ANON_KEY',
        'SUPABASE_SERVICE_ROLE_KEY',
        'DATABASE_URL',
        'NEXTAUTH_SECRET'
      ]
      
      let envPassed = 0
      for (const varName of requiredVars) {
        if (envContent.includes(varName) && envContent.includes('=')) {
          console.log(`✅ ${varName}: Configured`)
          envPassed++
        } else {
          console.log(`❌ ${varName}: Missing or empty`)
        }
      }
      
      if (envPassed === requiredVars.length) {
        console.log('✅ All environment variables configured')
        testResults.passed++
      } else {
        console.log(`⚠️  ${envPassed}/${requiredVars.length} environment variables configured`)
        testResults.warnings++
      }
    } else {
      console.log('❌ .env.local file not found')
      testResults.failed++
    }
  } catch (error) {
    console.log(`❌ Environment check failed: ${error.message}`)
    testResults.failed++
  }
  
  // Test 2: Check package.json and dependencies
  console.log('\n📦 Test 2: Dependencies Check')
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    const requiredDeps = [
      '@supabase/supabase-js',
      '@supabase/auth-helpers-nextjs',
      'next-auth',
      'prisma',
      '@prisma/client'
    ]
    
    let depsPassed = 0
    for (const dep of requiredDeps) {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        console.log(`✅ ${dep}: Installed`)
        depsPassed++
      } else {
        console.log(`❌ ${dep}: Missing`)
      }
    }
    
    if (depsPassed === requiredDeps.length) {
      console.log('✅ All required dependencies installed')
      testResults.passed++
    } else {
      console.log(`⚠️  ${depsPassed}/${requiredDeps.length} dependencies installed`)
      testResults.warnings++
    }
  } catch (error) {
    console.log(`❌ Dependencies check failed: ${error.message}`)
    testResults.failed++
  }
  
  // Test 3: Check Vercel configuration
  console.log('\n🚀 Test 3: Vercel Configuration')
  
  try {
    if (fs.existsSync('vercel.json')) {
      const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'))
      console.log('✅ vercel.json exists')
      
      if (vercelConfig.framework === 'nextjs') {
        console.log('✅ Framework set to Next.js')
      } else {
        console.log('⚠️  Framework not set to Next.js')
      }
      
      if (vercelConfig.functions) {
        console.log('✅ API functions configuration present')
      }
      
      testResults.passed++
    } else {
      console.log('❌ vercel.json not found')
      testResults.failed++
    }
  } catch (error) {
    console.log(`❌ Vercel configuration check failed: ${error.message}`)
    testResults.failed++
  }
  
  // Test 4: Check Next.js configuration
  console.log('\n⚙️  Test 4: Next.js Configuration')
  
  try {
    if (fs.existsSync('next.config.js')) {
      console.log('✅ next.config.js exists')
      
      const configContent = fs.readFileSync('next.config.js', 'utf8')
      if (configContent.includes('supabase.co')) {
        console.log('✅ Supabase domains configured')
      } else {
        console.log('⚠️  Supabase domains not found in config')
      }
      
      if (configContent.includes('standalone')) {
        console.log('✅ Standalone output configured for Vercel')
      } else {
        console.log('⚠️  Standalone output not configured')
      }
      
      testResults.passed++
    } else {
      console.log('❌ next.config.js not found')
      testResults.failed++
    }
  } catch (error) {
    console.log(`❌ Next.js configuration check failed: ${error.message}`)
    testResults.failed++
  }
  
  // Test 5: Test database connection
  console.log('\n🗄️  Test 5: Database Connection Test')
  
  try {
    // Run our existing comprehensive test
    const testProcess = spawn('node', ['comprehensive-test.js'], {
      stdio: 'pipe',
      cwd: process.cwd()
    })
    
    let output = ''
    testProcess.stdout.on('data', (data) => {
      output += data.toString()
    })
    
    testProcess.stderr.on('data', (data) => {
      output += data.toString()
    })
    
    await new Promise((resolve) => {
      testProcess.on('close', (code) => {
        if (output.includes('Success Rate:')) {
          const successMatch = output.match(/Success Rate: (\d+)%/)
          if (successMatch) {
            const successRate = parseInt(successMatch[1])
            if (successRate >= 80) {
              console.log(`✅ Database connection test passed (${successRate}% success rate)`)
              testResults.passed++
            } else {
              console.log(`⚠️  Database connection test partial (${successRate}% success rate)`)
              testResults.warnings++
            }
          }
        } else {
          console.log('❌ Database connection test failed')
          testResults.failed++
        }
        resolve()
      })
    })
  } catch (error) {
    console.log(`❌ Database connection test failed: ${error.message}`)
    testResults.failed++
  }
  
  // Test 6: Build test
  console.log('\n🔨 Test 6: Build Test')
  
  try {
    console.log('Running Next.js build test...')
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'pipe',
      cwd: process.cwd()
    })
    
    let buildOutput = ''
    buildProcess.stdout.on('data', (data) => {
      buildOutput += data.toString()
    })
    
    buildProcess.stderr.on('data', (data) => {
      buildOutput += data.toString()
    })
    
    await new Promise((resolve) => {
      buildProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Build test passed')
          testResults.passed++
        } else {
          console.log('❌ Build test failed')
          console.log('Build output:', buildOutput.slice(-500)) // Last 500 chars
          testResults.failed++
        }
        resolve()
      })
    })
  } catch (error) {
    console.log(`❌ Build test failed: ${error.message}`)
    testResults.failed++
  }
  
  // Final Results
  console.log('\n' + '='.repeat(60))
  console.log('🏥 LOCAL DEPLOYMENT TEST RESULTS')
  console.log('='.repeat(60))
  console.log(`✅ Tests Passed: ${testResults.passed}`)
  console.log(`⚠️  Warnings: ${testResults.warnings}`)
  console.log(`❌ Tests Failed: ${testResults.failed}`)
  console.log(`📊 Total Tests: ${testResults.passed + testResults.warnings + testResults.failed}`)
  
  const successRate = Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)
  console.log(`🎯 Success Rate: ${successRate}%`)
  
  if (testResults.failed === 0) {
    console.log('\n🎉 READY FOR VERCEL DEPLOYMENT!')
    console.log('✅ All tests passed')
    console.log('✅ Configuration verified')
    console.log('✅ Database connection working')
    console.log('✅ Build successful')
    console.log('\n📋 Next Steps:')
    console.log('1. Push your code to GitHub')
    console.log('2. Connect your GitHub repo to Vercel')
    console.log('3. Configure environment variables in Vercel')
    console.log('4. Deploy!')
  } else if (testResults.failed <= 2) {
    console.log('\n⚠️  MOSTLY READY FOR DEPLOYMENT')
    console.log('✅ Core functionality working')
    console.log('⚠️  Minor issues detected - review failed tests')
  } else {
    console.log('\n❌ NOT READY FOR DEPLOYMENT')
    console.log('❌ Multiple issues detected')
    console.log('🔧 Please fix failed tests before deploying')
  }
}

// Run the test
testLocalDeployment().catch(console.error)
