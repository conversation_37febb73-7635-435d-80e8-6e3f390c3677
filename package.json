{"name": "quant-nex-medical", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@react-three/drei": "^9.114.3", "@react-three/fiber": "^8.17.10", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^12.0.0", "lucide-react": "^0.454.0", "next": "^15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "prisma": "^6.11.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "recharts": "^2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.171.0", "zod": "^3.25.73"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/three": "^0.171.0", "autoprefixer": "^10.4.20", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}