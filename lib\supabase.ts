import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
})

// Client component Supabase client (for use in client components)
export const createSupabaseClient = () => {
  return createClientComponentClient()
}

// Server component Supabase client (for use in server components)
export const createSupabaseServerClient = () => {
  return createServerComponentClient({ cookies })
}

// Admin client with service role key (for server-side operations)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Database types (will be generated by Supabase CLI)
export interface Database {
  public: {
    Tables: {
      patients: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          first_name: string
          last_name: string
          email: string | null
          phone: string | null
          date_of_birth: string
          gender: 'male' | 'female' | 'other'
          address: string | null
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          medical_record_number: string
          cancer_type: string | null
          cancer_stage: 'I' | 'II' | 'III' | 'IV' | null
          diagnosis_date: string | null
          treatment_status: 'active' | 'remission' | 'critical' | 'deceased' | 'inactive'
          treatment_progress: number
          last_visit: string | null
          next_appointment: string | null
          assigned_doctor_id: string | null
          medical_history: string | null
          allergies: string[] | null
          current_medications: string[] | null
          family_history: string | null
          insurance_provider: string | null
          insurance_policy_number: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          first_name: string
          last_name: string
          email?: string | null
          phone?: string | null
          date_of_birth: string
          gender: 'male' | 'female' | 'other'
          address?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          medical_record_number: string
          cancer_type?: string | null
          cancer_stage?: 'I' | 'II' | 'III' | 'IV' | null
          diagnosis_date?: string | null
          treatment_status?: 'active' | 'remission' | 'critical' | 'deceased' | 'inactive'
          treatment_progress?: number
          last_visit?: string | null
          next_appointment?: string | null
          assigned_doctor_id?: string | null
          medical_history?: string | null
          allergies?: string[] | null
          current_medications?: string[] | null
          family_history?: string | null
          insurance_provider?: string | null
          insurance_policy_number?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          first_name?: string
          last_name?: string
          email?: string | null
          phone?: string | null
          date_of_birth?: string
          gender?: 'male' | 'female' | 'other'
          address?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          medical_record_number?: string
          cancer_type?: string | null
          cancer_stage?: 'I' | 'II' | 'III' | 'IV' | null
          diagnosis_date?: string | null
          treatment_status?: 'active' | 'remission' | 'critical' | 'deceased' | 'inactive'
          treatment_progress?: number
          last_visit?: string | null
          next_appointment?: string | null
          assigned_doctor_id?: string | null
          medical_history?: string | null
          allergies?: string[] | null
          current_medications?: string[] | null
          family_history?: string | null
          insurance_provider?: string | null
          insurance_policy_number?: string | null
        }
      }
      appointments: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          patient_id: string
          doctor_id: string
          appointment_date: string
          appointment_time: string
          duration_minutes: number
          type: 'consultation' | 'follow-up' | 'treatment' | 'telemedicine' | 'emergency'
          status: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'no-show'
          location: string | null
          room_number: string | null
          notes: string | null
          preparation_instructions: string | null
          reminder_sent: boolean
          created_by: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          patient_id: string
          doctor_id: string
          appointment_date: string
          appointment_time: string
          duration_minutes?: number
          type: 'consultation' | 'follow-up' | 'treatment' | 'telemedicine' | 'emergency'
          status?: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'no-show'
          location?: string | null
          room_number?: string | null
          notes?: string | null
          preparation_instructions?: string | null
          reminder_sent?: boolean
          created_by: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          patient_id?: string
          doctor_id?: string
          appointment_date?: string
          appointment_time?: string
          duration_minutes?: number
          type?: 'consultation' | 'follow-up' | 'treatment' | 'telemedicine' | 'emergency'
          status?: 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'no-show'
          location?: string | null
          room_number?: string | null
          notes?: string | null
          preparation_instructions?: string | null
          reminder_sent?: boolean
          created_by?: string
        }
      }
      medical_records: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          patient_id: string
          record_type: 'lab_result' | 'imaging' | 'pathology' | 'treatment_note' | 'progress_note' | 'discharge_summary'
          title: string
          content: string
          author_id: string
          author_name: string
          record_date: string
          attachments: string[] | null
          is_confidential: boolean
          tags: string[] | null
          related_appointment_id: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          patient_id: string
          record_type: 'lab_result' | 'imaging' | 'pathology' | 'treatment_note' | 'progress_note' | 'discharge_summary'
          title: string
          content: string
          author_id: string
          author_name: string
          record_date: string
          attachments?: string[] | null
          is_confidential?: boolean
          tags?: string[] | null
          related_appointment_id?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          patient_id?: string
          record_type?: 'lab_result' | 'imaging' | 'pathology' | 'treatment_note' | 'progress_note' | 'discharge_summary'
          title?: string
          content?: string
          author_id?: string
          author_name?: string
          record_date?: string
          attachments?: string[] | null
          is_confidential?: boolean
          tags?: string[] | null
          related_appointment_id?: string | null
        }
      }
      audit_logs: {
        Row: {
          id: string
          created_at: string
          user_id: string
          user_email: string
          action: string
          resource_type: string
          resource_id: string
          patient_id: string | null
          ip_address: string
          user_agent: string
          session_id: string
          outcome: 'success' | 'failure' | 'unauthorized'
          details: Record<string, any> | null
          phi_accessed: boolean
          data_classification: 'public' | 'internal' | 'confidential' | 'restricted'
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          user_email: string
          action: string
          resource_type: string
          resource_id: string
          patient_id?: string | null
          ip_address: string
          user_agent: string
          session_id: string
          outcome: 'success' | 'failure' | 'unauthorized'
          details?: Record<string, any> | null
          phi_accessed?: boolean
          data_classification: 'public' | 'internal' | 'confidential' | 'restricted'
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          user_email?: string
          action?: string
          resource_type?: string
          resource_id?: string
          patient_id?: string | null
          ip_address?: string
          user_agent?: string
          session_id?: string
          outcome?: 'success' | 'failure' | 'unauthorized'
          details?: Record<string, any> | null
          phi_accessed?: boolean
          data_classification?: 'public' | 'internal' | 'confidential' | 'restricted'
        }
      }
      users: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          email: string
          first_name: string
          last_name: string
          role: 'doctor' | 'nurse' | 'admin' | 'researcher' | 'technician'
          specialization: string | null
          license_number: string
          department: string
          phone: string | null
          is_active: boolean
          last_login: string | null
          permissions: string[]
          avatar_url: string | null
        }
        Insert: {
          id: string
          created_at?: string
          updated_at?: string
          email: string
          first_name: string
          last_name: string
          role: 'doctor' | 'nurse' | 'admin' | 'researcher' | 'technician'
          specialization?: string | null
          license_number: string
          department: string
          phone?: string | null
          is_active?: boolean
          last_login?: string | null
          permissions?: string[]
          avatar_url?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          email?: string
          first_name?: string
          last_name?: string
          role?: 'doctor' | 'nurse' | 'admin' | 'researcher' | 'technician'
          specialization?: string | null
          license_number?: string
          department?: string
          phone?: string | null
          is_active?: boolean
          last_login?: string | null
          permissions?: string[]
          avatar_url?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
