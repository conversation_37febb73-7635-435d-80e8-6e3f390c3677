{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install --legacy-peer-deps", "devCommand": "npm run dev", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(), payment=()"}]}, {"source": "/(.*)", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.sentry-cdn.com https://vercel.live; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co https://*.sentry.io https://vercel.live; frame-ancestors 'none'; base-uri 'self'; form-action 'self';"}]}], "redirects": [{"source": "/health", "destination": "/api/health", "permanent": false}], "rewrites": [{"source": "/api/auth/(.*)", "destination": "/api/auth/$1"}], "env": {"NEXT_TELEMETRY_DISABLED": "1"}}