# 🏥 Quant-NEX Medical Application - Production Setup Guide

## 📋 Overview

This guide provides step-by-step instructions for setting up the Quant-NEX medical application in a production environment with HIPAA compliance, security, and monitoring.

## 🔧 Prerequisites

### System Requirements
- **Server**: Linux (Ubuntu 20.04+ recommended)
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores
- **Network**: HTTPS/SSL certificate required

### Required Services
- **Database**: Supabase (PostgreSQL)
- **Error Monitoring**: Sentry
- **Rate Limiting**: Upstash Redis (optional)
- **Container Runtime**: Docker & Docker Compose

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone https://github.com/your-org/quant-nex.git
cd quant-nex

# Copy environment template
cp .env.example .env.local

# Make deployment script executable
chmod +x scripts/deploy.sh
```

### 2. Configure Environment Variables

Edit `.env.local` with your production values:

```bash
# Database Configuration
DATABASE_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
DIRECT_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://[PROJECT-REF].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Authentication
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-secure-secret-key

# Error Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_DSN=https://<EMAIL>/project-id

# Optional: Rate Limiting
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token
```

### 3. Database Setup

#### Option A: Supabase (Recommended)

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Run the database migration:
   ```bash
   # Copy the SQL from supabase/migrations/001_initial_schema.sql
   # Execute it in your Supabase SQL editor
   ```
3. Configure Row Level Security (RLS) policies
4. Set up authentication providers

#### Option B: Self-hosted PostgreSQL

```bash
# Start PostgreSQL with Docker
docker-compose up postgres -d

# Run migrations
npx prisma migrate deploy
npx prisma generate
```

### 4. Error Monitoring Setup

1. Create a Sentry account at [sentry.io](https://sentry.io)
2. Create a new project for "Next.js"
3. Copy the DSN to your environment variables
4. Configure alerts and integrations

### 5. SSL Certificate Setup

```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates to nginx directory
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ./ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ./ssl/key.pem
```

### 6. Deploy Application

```bash
# Production deployment
./scripts/deploy.sh

# Check deployment status
./scripts/deploy.sh health
```

## 🔒 Security Configuration

### HIPAA Compliance Checklist

- [ ] **Encryption at Rest**: Database encryption enabled
- [ ] **Encryption in Transit**: HTTPS/TLS 1.2+ enforced
- [ ] **Access Controls**: Role-based permissions implemented
- [ ] **Audit Logging**: All PHI access logged
- [ ] **Data Backup**: Automated encrypted backups
- [ ] **User Authentication**: Multi-factor authentication
- [ ] **Session Management**: Secure session handling
- [ ] **Data Minimization**: Only necessary data collected

### Security Headers

The application includes comprehensive security headers:
- Content Security Policy (CSP)
- HTTP Strict Transport Security (HSTS)
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

### Rate Limiting

API endpoints are protected with rate limiting:
- Authentication: 5 requests/minute
- API endpoints: 100 requests/minute
- General pages: 20 requests/minute

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Application health
curl https://your-domain.com/api/health

# Database connectivity
curl https://your-domain.com/api/health | jq '.services.database'

# Error monitoring
# Check Sentry dashboard for errors and performance
```

### Log Management

```bash
# Application logs
docker-compose logs app

# Nginx access logs
tail -f /var/log/nginx/access.log

# System logs
journalctl -u docker
```

### Backup Strategy

```bash
# Database backup (automated in deployment script)
pg_dump $DATABASE_URL > backup-$(date +%Y%m%d).sql

# Application backup
tar -czf app-backup-$(date +%Y%m%d).tar.gz .
```

## 🔄 Updates and Maintenance

### Regular Updates

```bash
# Update dependencies
npm audit fix

# Update Docker images
docker-compose pull
./scripts/deploy.sh

# Update SSL certificates (automated with certbot)
sudo certbot renew
```

### Database Migrations

```bash
# Create new migration
npx prisma migrate dev --name migration_name

# Deploy to production
npx prisma migrate deploy
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database URL format
   echo $DATABASE_URL
   
   # Test connection
   psql $DATABASE_URL -c "SELECT 1;"
   ```

2. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in ssl/cert.pem -text -noout
   
   # Renew certificate
   sudo certbot renew
   ```

3. **High Memory Usage**
   ```bash
   # Check container memory usage
   docker stats
   
   # Restart containers
   docker-compose restart
   ```

### Emergency Procedures

#### Rollback Deployment
```bash
./scripts/deploy.sh rollback
```

#### Database Recovery
```bash
# Restore from backup
psql $DATABASE_URL < backup-YYYYMMDD.sql
```

#### Security Incident Response
1. Immediately revoke compromised credentials
2. Check audit logs for unauthorized access
3. Notify relevant stakeholders
4. Document incident for compliance

## 📞 Support

### Production Support Contacts
- **Technical Issues**: <EMAIL>
- **Security Incidents**: <EMAIL>
- **HIPAA Compliance**: <EMAIL>

### Documentation
- [API Documentation](./docs/api.md)
- [Security Policies](./docs/security.md)
- [HIPAA Compliance](./docs/hipaa.md)

## 📝 Compliance Documentation

### Required Documentation
- [ ] Risk Assessment
- [ ] Security Policies
- [ ] Incident Response Plan
- [ ] Data Backup Procedures
- [ ] User Access Controls
- [ ] Audit Log Procedures

### Audit Trail
All system access and PHI interactions are logged with:
- User identification
- Timestamp
- Action performed
- Resource accessed
- Outcome (success/failure)
- IP address and session information

## 🎯 Performance Optimization

### Production Optimizations
- Next.js static generation
- Image optimization
- Bundle splitting
- CDN integration
- Database connection pooling
- Redis caching (optional)

### Monitoring Metrics
- Response time < 2 seconds
- Uptime > 99.9%
- Error rate < 0.1%
- Database query time < 100ms

---

**⚠️ Important**: This is a medical application handling Protected Health Information (PHI). Ensure all team members are trained on HIPAA compliance requirements before accessing production systems.
