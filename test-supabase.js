// Simple Supabase Connection Test
// Run this with: node test-supabase.js

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const supabaseUrl = 'https://tyrvzdkuruzpojowctjo.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR5cnZ6ZGt1cnV6cG9qb3djdGpvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxMjM1ODUsImV4cCI6MjA2MjY5OTU4NX0._-d60z0kj7jzBc_PvU635OdlWjxKmlvfUWMNT8MCeYE'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase Connection...\n')
  
  try {
    // Test 1: Check if we can connect to Supabase
    console.log('1. Testing basic connection...')
    const { data, error } = await supabase.from('profiles').select('count').limit(1)
    
    if (error) {
      console.log('❌ Connection failed:', error.message)
      return
    }
    console.log('✅ Basic connection successful!')
    
    // Test 2: Check all tables exist
    console.log('\n2. Checking if all tables exist...')
    const tables = ['profiles', 'medical_professionals', 'patients', 'appointments', 'medical_records', 'audit_logs']
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('count').limit(1)
        if (error) {
          console.log(`❌ Table '${table}' not found or accessible:`, error.message)
        } else {
          console.log(`✅ Table '${table}' exists and accessible`)
        }
      } catch (err) {
        console.log(`❌ Error checking table '${table}':`, err.message)
      }
    }
    
    // Test 3: Check sample data
    console.log('\n3. Checking sample data...')
    
    // Check medical professionals
    const { data: professionals, error: profError } = await supabase
      .from('medical_professionals')
      .select('*')
      .limit(5)
    
    if (profError) {
      console.log('❌ Error fetching medical professionals:', profError.message)
    } else {
      console.log(`✅ Found ${professionals.length} medical professionals`)
      if (professionals.length > 0) {
        console.log('   Sample professional:', professionals[0].role, '-', professionals[0].department)
      }
    }
    
    // Check patients
    const { data: patients, error: patError } = await supabase
      .from('patients')
      .select('*')
      .limit(5)
    
    if (patError) {
      console.log('❌ Error fetching patients:', patError.message)
    } else {
      console.log(`✅ Found ${patients.length} patients`)
      if (patients.length > 0) {
        console.log('   Sample patient:', patients[0].first_name, patients[0].last_name)
      }
    }
    
    // Test 4: Test Row Level Security
    console.log('\n4. Testing Row Level Security...')
    
    // This should work (public read access)
    const { data: publicData, error: publicError } = await supabase
      .from('patients')
      .select('count')
      .limit(1)
    
    if (publicError) {
      console.log('⚠️  RLS is working (expected for unauthenticated users):', publicError.message)
    } else {
      console.log('✅ Public access working (or RLS not fully configured)')
    }
    
    console.log('\n🎉 Supabase verification complete!')
    console.log('\n📋 Summary:')
    console.log('- Database connection: Working')
    console.log('- Tables created: Check individual results above')
    console.log('- Sample data: Check individual results above')
    console.log('- Security: RLS policies are active')
    
  } catch (error) {
    console.log('❌ Unexpected error:', error.message)
  }
}

// Run the test
testSupabaseConnection()
