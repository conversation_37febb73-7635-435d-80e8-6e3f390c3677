// Simple verification script using fetch API
// Run this with: node verify-setup.js

const supabaseUrl = 'https://tyrvzdkuruzpojowctjo.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._-d60z0kj7jzBc_PvU635OdlWjxKmlvfUWMNT8MCeYE'

async function verifySupabaseSetup() {
  console.log('🔍 Verifying Supabase Setup...\n')
  
  const headers = {
    'apikey': supabaseKey,
    'Authorization': `Bearer ${supabaseKey}`,
    'Content-Type': 'application/json'
  }
  
  // Test tables
  const tables = ['profiles', 'medical_professionals', 'patients', 'appointments', 'medical_records', 'audit_logs']
  
  console.log('📋 Checking Tables:')
  
  for (const table of tables) {
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/${table}?select=count&limit=1`, {
        method: 'GET',
        headers: headers
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${table}: Accessible`)
      } else {
        const error = await response.text()
        console.log(`❌ ${table}: Error - ${response.status} ${response.statusText}`)
        if (response.status === 401) {
          console.log('   → This might be due to RLS policies (expected)')
        }
      }
    } catch (error) {
      console.log(`❌ ${table}: Network error - ${error.message}`)
    }
  }
  
  // Test sample data
  console.log('\n📊 Checking Sample Data:')
  
  try {
    // Check medical professionals
    const profResponse = await fetch(`${supabaseUrl}/rest/v1/medical_professionals?select=*&limit=5`, {
      method: 'GET',
      headers: headers
    })
    
    if (profResponse.ok) {
      const professionals = await profResponse.json()
      console.log(`✅ Medical Professionals: ${professionals.length} records found`)
      if (professionals.length > 0) {
        console.log(`   → Sample: ${professionals[0].role} in ${professionals[0].department}`)
      }
    } else {
      console.log(`⚠️  Medical Professionals: ${profResponse.status} - Might be RLS protected`)
    }
    
    // Check patients
    const patResponse = await fetch(`${supabaseUrl}/rest/v1/patients?select=*&limit=5`, {
      method: 'GET',
      headers: headers
    })
    
    if (patResponse.ok) {
      const patients = await patResponse.json()
      console.log(`✅ Patients: ${patients.length} records found`)
      if (patients.length > 0) {
        console.log(`   → Sample: ${patients[0].first_name} ${patients[0].last_name}`)
      }
    } else {
      console.log(`⚠️  Patients: ${patResponse.status} - Might be RLS protected`)
    }
    
  } catch (error) {
    console.log(`❌ Error checking sample data: ${error.message}`)
  }
  
  // Test environment variables
  console.log('\n🔧 Environment Configuration:')
  
  const envFile = '.env.local'
  const fs = require('fs')
  
  try {
    if (fs.existsSync(envFile)) {
      console.log('✅ .env.local file exists')
      const envContent = fs.readFileSync(envFile, 'utf8')
      
      const requiredVars = [
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_SUPABASE_ANON_KEY',
        'SUPABASE_SERVICE_ROLE_KEY',
        'DATABASE_URL',
        'DIRECT_URL'
      ]
      
      for (const varName of requiredVars) {
        if (envContent.includes(varName)) {
          console.log(`✅ ${varName}: Configured`)
        } else {
          console.log(`❌ ${varName}: Missing`)
        }
      }
    } else {
      console.log('❌ .env.local file not found')
    }
  } catch (error) {
    console.log(`❌ Error checking environment: ${error.message}`)
  }
  
  console.log('\n🎉 Verification Complete!')
  console.log('\n📋 Next Steps:')
  console.log('1. If tables show as accessible ✅ - Your setup is working!')
  console.log('2. If you see RLS errors ⚠️  - This is normal for unauthenticated requests')
  console.log('3. If you see network errors ❌ - Check your Supabase URL and keys')
  console.log('4. Ready to test the application!')
}

// Run verification
verifySupabaseSetup().catch(console.error)
